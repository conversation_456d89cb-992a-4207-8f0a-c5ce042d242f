using System;
using HIH.Framework.AutoCrawingManager.Process;

class TestJsonParsing
{
    static void Main()
    {
        Console.WriteLine("=== JSON解析测试 ===");
        Console.WriteLine();

        // 创建IONE实例
        IONE oneProcessor = new IONE();

        // 测试用例1: 有效的JSON数据
        Console.WriteLine("测试用例1: 有效的JSON数据");
        string validJson = @"[
            {
                ""containerNo"": ""NYKU0719647"",
                ""detailHtml"": ""<html><body>测试HTML内容</body></html>""
            }
        ]";
        
        string result1 = oneProcessor.TestJsonParsing(validJson);
        Console.WriteLine($"结果: {result1}");
        Console.WriteLine();

        // 测试用例2: 包含复杂HTML的JSON数据（类似您提供的数据）
        Console.WriteLine("测试用例2: 包含复杂HTML的JSON数据");
        string complexJson = @"[{""containerNo"":""NYKU0719647"",""detailHtml"":""<head><meta charset=\""utf-8\""><title>ONE : Cargo Tracking</title></head><body>测试内容</body>""}]";
        
        string result2 = oneProcessor.TestJsonParsing(complexJson);
        Console.WriteLine($"结果: {result2}");
        Console.WriteLine();

        // 测试用例3: 无效的JSON数据
        Console.WriteLine("测试用例3: 无效的JSON数据");
        string invalidJson = @"[{containerNo:NYKU0719647,detailHtml:invalid}]"; // 缺少引号
        
        string result3 = oneProcessor.TestJsonParsing(invalidJson);
        Console.WriteLine($"结果: {result3}");
        Console.WriteLine();

        // 测试用例4: 空数据
        Console.WriteLine("测试用例4: 空数据");
        string emptyJson = "";
        
        string result4 = oneProcessor.TestJsonParsing(emptyJson);
        Console.WriteLine($"结果: {result4}");
        Console.WriteLine();

        // 测试用例5: 非数组格式
        Console.WriteLine("测试用例5: 非数组格式");
        string nonArrayJson = @"{""containerNo"":""NYKU0719647""}";
        
        string result5 = oneProcessor.TestJsonParsing(nonArrayJson);
        Console.WriteLine($"结果: {result5}");
        Console.WriteLine();

        Console.WriteLine("=== 测试完成 ===");
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
